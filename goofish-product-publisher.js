/**
 * 闲鱼商品自动发布脚本
 * 基于 Playwright 自动化浏览器操作
 * 
 * 功能：
 * 1. 自动打开闲鱼商品发布页面
 * 2. 填写商品分类和属性
 * 3. 上传商品图片
 * 4. 填写商品信息
 * 5. 设置价格和库存
 * 6. 发布商品
 */

const { chromium } = require('playwright');
const path = require('path');

class GoofishProductPublisher {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
    }

    /**
     * 初始化浏览器
     */
    async init(options = {}) {
        const os = require('os');
        const defaultUserDataDir = path.join(
            os.homedir(),
            process.platform === 'win32'
                ? 'AppData/Local/ms-playwright/mcp-chrome'
                : process.platform === 'darwin'
                ? 'Library/Caches/ms-playwright/mcp-chrome'
                : '.cache/ms-playwright/mcp-chrome-profile'
        );
        console.log('defaultUserDataDir', defaultUserDataDir)

        const userDataDir = options.userDataDir || defaultUserDataDir;
        const usePersistentContext = options.usePersistentContext !== false; // 默认使用持久化上下文

        if (usePersistentContext) {
            // 使用持久化上下文，支持用户数据目录
            const contextOptions = {
                headless: false,
                slowMo: 1000,
                viewport: { width: 1920, height: 1080 },
                ...options.contextOptions
            };

            this.context = await chromium.launchPersistentContext(userDataDir, contextOptions);
            this.browser = this.context.browser();
            this.page = this.context.pages()[0] || await this.context.newPage();

            console.log(`🗂️ 使用持久化用户数据目录: ${userDataDir}`);
        } else {
            // 使用普通浏览器启动（不保存用户数据）
            const launchOptions = {
                headless: false,
                slowMo: 1000,
                ...options.launchOptions
            };

            this.browser = await chromium.launch(launchOptions);
            this.context = await this.browser.newContext({
                viewport: { width: 1920, height: 1080 },
                ...options.contextOptions
            });
            this.page = await this.context.newPage();

            console.log('🗂️ 使用临时浏览器会话（不保存用户数据）');
        }
    }

    /**
     * 打开商品发布页面
     */
    async openProductAddPage() {
        console.log('🌐 正在打开商品发布页面...');
        await this.page.goto('https://www.goofish.pro/login');
        await this.page.waitForLoadState('networkidle');
        console.log('✅ 页面加载完成');
    }

    /**
     * 选择商品分类
     */
    async selectCategory() {
        console.log('📂 正在选择商品分类...');
        
        // 点击商品分类输入框
        await this.page.click('input[placeholder="请输入关键词"]');
        await this.page.waitForTimeout(1000);

        // 选择3C数码
        await this.page.click('text=3C数码');
        await this.page.waitForTimeout(1000);

        // 选择笔记本电脑
        await this.page.click('text=笔记本电脑');
        await this.page.waitForTimeout(1000);

        // 选择最终分类
        const laptopOptions = await this.page.$$('text=笔记本电脑');
        if (laptopOptions.length > 0) {
            await laptopOptions[laptopOptions.length - 1].click();
        }
        
        console.log('✅ 商品分类选择完成：3C数码/笔记本电脑');
    }

    /**
     * 填写商品属性
     */
    async fillProductAttributes() {
        console.log('🏷️ 正在填写商品属性...');

        // 选择品牌：Apple/苹果
        await this.page.click('text=Apple/苹果');
        await this.page.waitForTimeout(1000);

        // 选择型号：MacBook Pro 2023 14寸
        await this.page.click('text=MacBook Pro 2023 14寸');
        await this.page.waitForTimeout(1000);

        // 选择成色：几乎全新
        await this.page.click('text=几乎全新');
        await this.page.waitForTimeout(1000);

        // 选择购买渠道：中国大陆
        await this.page.click('text=中国大陆');
        await this.page.waitForTimeout(1000);

        // 选择内存容量：16GB
        await this.page.click('text=16GB');
        await this.page.waitForTimeout(1000);

        console.log('✅ 商品属性填写完成');
    }

    /**
     * 上传商品图片
     */
    async uploadProductImage(imagePath) {
        console.log('🖼️ 正在上传商品图片...');
        
        // 点击上传图片按钮
        const uploadButton = await this.page.$('text=上传图片');
        if (uploadButton) {
            // 设置文件选择器
            const [fileChooser] = await Promise.all([
                this.page.waitForEvent('filechooser'),
                uploadButton.click()
            ]);
            
            // 上传文件
            await fileChooser.setFiles(imagePath);
            await this.page.waitForTimeout(3000); // 等待图片上传完成
            
            console.log('✅ 图片上传完成');
        }
    }

    /**
     * 填写商品信息
     */
    async fillProductInfo(productData) {
        console.log('📝 正在填写商品信息...');

        // 填写商品标题
        const titleInput = await this.page.$('input[placeholder*="商品标题"]');
        if (titleInput) {
            await titleInput.fill(productData.title);
        }

        // 填写商品描述
        const descInput = await this.page.$('textarea[placeholder*="商品描述"]');
        if (descInput) {
            await descInput.fill(productData.description);
        }

        console.log('✅ 商品信息填写完成');
    }

    /**
     * 设置价格和库存
     */
    async setPriceAndStock(price, stock = 1) {
        console.log('💰 正在设置价格和库存...');

        // 设置售价
        const priceInput = await this.page.$('input[placeholder*="0.00"]');
        if (priceInput) {
            await priceInput.fill(price.toString());
        }

        // 设置库存（默认为1）
        const stockInputs = await this.page.$$('input[type="number"]');
        for (let input of stockInputs) {
            const value = await input.inputValue();
            if (value === '1' || value === '') {
                await input.fill(stock.toString());
                break;
            }
        }

        console.log(`✅ 价格设置为：¥${price}，库存：${stock}件`);
    }

    /**
     * 发布商品
     */
    async publishProduct() {
        console.log('🚀 正在发布商品...');

        // 点击确定按钮
        const confirmButton = await this.page.$('button:has-text("确定")');
        if (confirmButton) {
            await confirmButton.click();
            
            // 等待发布完成
            await this.page.waitForTimeout(3000);
            
            // 检查是否发布成功
            const successMessage = await this.page.$('text=发布成功');
            if (successMessage) {
                console.log('🎉 商品发布成功！');
                return true;
            }
        }
        
        return false;
    }

    /**
     * 主要发布流程
     */
    async publishProductFlow(productData, imagePath, options = {}) {
        try {
            await this.init(options);
            await this.openProductAddPage();
            return;
            await this.selectCategory();
            await this.fillProductAttributes();
            await this.uploadProductImage(imagePath);
            await this.fillProductInfo(productData);
            await this.setPriceAndStock(productData.price, productData.stock);

            const success = await this.publishProduct();

            if (success) {
                console.log('✅ 商品发布流程完成！');
            } else {
                console.log('❌ 商品发布失败，请检查页面状态');
            }

            // 等待5秒后关闭浏览器
            await this.page.waitForTimeout(5000);

        } catch (error) {
            console.error('❌ 发布过程中出现错误：', error);
        } finally {
            // await this.close();
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        try {
            if (this.context) {
                await this.context.close();
            } else if (this.browser) {
                await this.browser.close();
            }
        } catch (error) {
            console.warn('⚠️ 关闭浏览器时出现警告：', error.message);
        }
    }
}

// 使用示例
async function main() {
    const publisher = new GoofishProductPublisher();

    // 商品数据
    const productData = {
        title: '苹果MacBook Pro 2023 14寸 16GB 几乎全新',
        description: '苹果MacBook Pro 2023款 14寸笔记本电脑，配置16GB内存，几乎全新成色，中国大陆购买，无任何拆修记录。外观精美，性能强劲，适合办公、设计、编程等多种用途。原装配件齐全，支持包邮发货。',
        price: 12800,
        stock: 1
    };

    // 图片路径（请确保文件存在）
    const imagePath = path.join(__dirname, 'apple.jpg');

    // 浏览器选项 - 使用与MCP Playwright相同的用户数据目录
    const options = {
        usePersistentContext: true, // 使用持久化上下文
        userDataDir: undefined, // 使用默认MCP目录
        contextOptions: {
            headless: false,
            slowMo: 1000,
            viewport: { width: 1920, height: 1080 }
        }
    };

    // 执行发布流程
    await publisher.publishProductFlow(productData, imagePath, options);
}

// 运行脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = GoofishProductPublisher;
